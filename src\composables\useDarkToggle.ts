import { nextTick, watch } from 'vue'

export interface DarkToggleOptions {
  /**
   * 动画持续时间（毫秒）
   * @default 400
   */
  duration?: number
  /**
   * 动画缓动函数
   * @default 'ease-out'
   */
  easing?: string
  /**
   * 是否启用视图过渡动画
   * @default true
   */
  enableTransition?: boolean
}

/**
 * 同步设置 body 元素的 arco-theme 属性
 * @param isDark 是否为深色模式
 */
function syncArcoTheme(isDark: boolean) {
  if (typeof document !== 'undefined') {
    document.body.setAttribute('arco-theme', isDark ? 'dark' : 'light')
  }
}

/**
 * 深色模式切换 Hook
 * 提供带有圆形展开动画的深色模式切换功能
 */
export function useDarkToggle(options: DarkToggleOptions = {}) {
  const {
    duration = 400,
    easing = 'ease-out',
    enableTransition = true,
  } = options

  // 使用 ref 来管理暗色模式状态，不依赖 useColorMode
  const isDark = ref(false)

  // 初始化时从 localStorage 读取状态
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('arco-theme')
    if (stored) {
      isDark.value = stored === 'dark'
    } else {
      // 如果没有存储值，检查系统偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      isDark.value = prefersDark
    }
    // 立即设置初始状态
    syncArcoTheme(isDark.value)
  }

  // 监听暗色模式变化，同步设置 arco-theme 属性和 localStorage
  watch(
    isDark,
    (newValue) => {
      syncArcoTheme(newValue)
      if (typeof window !== 'undefined') {
        localStorage.setItem('arco-theme', newValue ? 'dark' : 'light')
      }
    }
  )

  /**
   * 切换深色模式
   * @param event 鼠标事件，用于获取点击位置作为动画起点
   */
  const toggleDark = (event: MouseEvent) => {
    // 检查是否支持动画和用户偏好
    const isAppearanceTransition = !window.matchMedia(
      '(prefers-reduced-motion: reduce)',
    ).matches

    // 如果不支持动画或用户禁用了动画，直接切换
    if (
      !enableTransition ||
      !isAppearanceTransition ||
      typeof document.startViewTransition !== 'function'
    ) {
      isDark.value = !isDark.value
      return
    }

    // 获取点击位置
    const x = event.clientX
    const y = event.clientY

    // 计算从点击点到屏幕边缘的最大距离，作为圆形动画的半径
    const endRadius = Math.hypot(
      Math.max(x, innerWidth - x),
      Math.max(y, innerHeight - y),
    )

    // 启动视图过渡
    const transition = document.startViewTransition(async () => {
      isDark.value = !isDark.value
      await nextTick()
    })

    // 当过渡准备就绪时，添加圆形展开动画
    transition.ready.then(() => {
      const clipPath = [
        `circle(0px at ${x}px ${y}px)`,
        `circle(${endRadius}px at ${x}px ${y}px)`,
      ]

      document.documentElement.animate(
        {
          clipPath: isDark.value ? [...clipPath].reverse() : clipPath,
        },
        {
          duration,
          easing,
          pseudoElement:
            isDark.value
              ? '::view-transition-old(root)'
              : '::view-transition-new(root)',
        },
      )
    })
  }

  /**
   * 简单切换深色模式（无动画）
   */
  const toggleDarkSimple = () => {
    isDark.value = !isDark.value
  }

  return {
    isDark,
    toggleDark,
    toggleDarkSimple,
  }
}
