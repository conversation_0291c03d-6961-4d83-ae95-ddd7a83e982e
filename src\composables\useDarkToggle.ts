import { nextTick, watch } from 'vue'

export interface DarkToggleOptions {
  /**
   * 动画持续时间（毫秒）
   * @default 400
   */
  duration?: number
  /**
   * 动画缓动函数
   * @default 'ease-out'
   */
  easing?: string
  /**
   * 是否启用视图过渡动画
   * @default true
   */
  enableTransition?: boolean
}

/**
 * 同步设置 body 元素的 arco-theme 属性
 * @param isDark 是否为深色模式
 */
function syncArcoTheme(isDark: boolean) {
  if (typeof document !== 'undefined') {
    document.body.setAttribute('arco-theme', isDark ? 'dark' : 'light')
  }
}

/**
 * 深色模式切换 Hook
 * 提供带有圆形展开动画的深色模式切换功能
 */
export function useDarkToggle(options: DarkToggleOptions = {}) {
  const {
    duration = 400,
    easing = 'ease-out',
    enableTransition = true,
  } = options

  const colorMode = useColorMode()

  // 监听颜色模式变化，同步设置 arco-theme 属性
  watch(
    () => colorMode.value,
    (newMode) => {
      syncArcoTheme(newMode === 'dark')
    },
    { immediate: true }
  )

  /**
   * 切换深色模式
   * @param event 鼠标事件，用于获取点击位置作为动画起点
   */
  const toggleDark = (event: MouseEvent) => {
    // 检查是否支持动画和用户偏好
    const isAppearanceTransition = !window.matchMedia(
      '(prefers-reduced-motion: reduce)',
    ).matches

    // 如果不支持动画或用户禁用了动画，直接切换
    if (
      !enableTransition ||
      !isAppearanceTransition ||
      typeof document.startViewTransition !== 'function'
    ) {
      const newMode = colorMode.value === 'dark' ? 'light' : 'dark'
      colorMode.value = newMode
      syncArcoTheme(newMode === 'dark')
      return
    }

    // 获取点击位置
    const x = event.clientX
    const y = event.clientY

    // 计算从点击点到屏幕边缘的最大距离，作为圆形动画的半径
    const endRadius = Math.hypot(
      Math.max(x, innerWidth - x),
      Math.max(y, innerHeight - y),
    )

    // 启动视图过渡
    const transition = document.startViewTransition(async () => {
      const newMode = colorMode.value === 'dark' ? 'light' : 'dark'
      colorMode.value = newMode
      syncArcoTheme(newMode === 'dark')
      await nextTick()
    })

    // 当过渡准备就绪时，添加圆形展开动画
    transition.ready.then(() => {
      const clipPath = [
        `circle(0px at ${x}px ${y}px)`,
        `circle(${endRadius}px at ${x}px ${y}px)`,
      ]

      document.documentElement.animate(
        {
          clipPath: colorMode.value === 'dark' ? [...clipPath].reverse() : clipPath,
        },
        {
          duration,
          easing,
          pseudoElement:
            colorMode.value === 'dark'
              ? '::view-transition-old(root)'
              : '::view-transition-new(root)',
        },
      )
    })
  }

  /**
   * 简单切换深色模式（无动画）
   */
  const toggleDarkSimple = () => {
    const newMode = colorMode.value === 'dark' ? 'light' : 'dark'
    colorMode.value = newMode
    syncArcoTheme(newMode === 'dark')
  }

  return {
    colorMode,
    toggleDark,
    toggleDarkSimple,
    isDark: computed(() => colorMode.value === 'dark'),
  }
}
