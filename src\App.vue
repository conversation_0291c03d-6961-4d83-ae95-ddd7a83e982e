<template>
  <a-config-provider update-at-scroll>
    <RouterView />
  </a-config-provider>
</template>

<style lang="less">
html,
body,
#app {
  height: 100%;
}

html {
  background: #ecf0f1;
  color: #121212;
}

html.dark {
  color-scheme: dark;
  background: #121212;
  color: #ecf0f1;
}

::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-old(root) {
  z-index: 1;
}

::view-transition-new(root) {
  z-index: 9999;
}

.dark::view-transition-old(root) {
  z-index: 9999;
}

.dark::view-transition-new(root) {
  z-index: 1;
}
</style>