{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON><PERSON><PERSON> Stock Helper", "version": "0.1.0", "identifier": "com.qingshan.stock-helper", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON><PERSON><PERSON> Stock Helper", "width": 1200, "height": 800, "decorations": false, "center": true, "shadow": true, "dragDropEnabled": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": null, "wix": {"language": "en-US", "template": null}}}}