{"rustc": 3062648155896360161, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 8778928345189965635, "deps": [[3060637413840920116, "proc_macro2", false, 11261785634564507337], [7341521034400937459, "tauri_codegen", false, 17731378776694420619], [11050281405049894993, "tauri_utils", false, 7509034364537852423], [13077543566650298139, "heck", false, 7238350878503789985], [17990358020177143287, "quote", false, 9751866052406781826], [18149961000318489080, "syn", false, 14868330989185878021]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-cb7509ab86eab5d7\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}