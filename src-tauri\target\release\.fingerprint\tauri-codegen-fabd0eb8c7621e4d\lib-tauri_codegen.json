{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 8985965904563823836, "deps": [[3060637413840920116, "proc_macro2", false, 11261785634564507337], [3150220818285335163, "url", false, 538572273215624548], [4899080583175475170, "semver", false, 9889865680198083971], [7170110829644101142, "json_patch", false, 7396632103335079930], [7392050791754369441, "ico", false, 14102234043177596973], [8319709847752024821, "uuid", false, 9445340529449502681], [9689903380558560274, "serde", false, 7001324394923115962], [9857275760291862238, "sha2", false, 1523213661238095905], [10806645703491011684, "thiserror", false, 12976510703586073783], [11050281405049894993, "tauri_utils", false, 7509034364537852423], [12687914511023397207, "png", false, 5935494101426165565], [13077212702700853852, "base64", false, 6378895984677879606], [14132538657330703225, "brotli", false, 4643452673632649092], [15367738274754116744, "serde_json", false, 6545467989993011207], [15622660310229662834, "walkdir", false, 2170879548194002287], [17990358020177143287, "quote", false, 9751866052406781826], [18149961000318489080, "syn", false, 14868330989185878021]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-fabd0eb8c7621e4d\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}