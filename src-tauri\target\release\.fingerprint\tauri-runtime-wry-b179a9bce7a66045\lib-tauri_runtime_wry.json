{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 17231900515118247201, "deps": [[376837177317575824, "softbuffer", false, 15882075278854583474], [442785307232013896, "tauri_runtime", false, 8332037655137749387], [3150220818285335163, "url", false, 12933449415935540558], [3722963349756955755, "once_cell", false, 4567159275817069478], [4143744114649553716, "raw_window_handle", false, 14026015622225031953], [5986029879202738730, "log", false, 6945410041022675698], [7752760652095876438, "build_script_build", false, 12932120951390438425], [8539587424388551196, "webview2_com", false, 4012651261636751816], [9010263965687315507, "http", false, 6524139142051382750], [11050281405049894993, "tauri_utils", false, 3635780141620643065], [13116089016666501665, "windows", false, 17693621379593172598], [13223659721939363523, "tao", false, 5745370189329454721], [14794439852947137341, "wry", false, 16834977658088840430]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-b179a9bce7a66045\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}