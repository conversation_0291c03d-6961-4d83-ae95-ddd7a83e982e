# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-unlisten"
description = "Enables the unlisten command without any pre-configured scope."
commands.allow = ["unlisten"]

[[permission]]
identifier = "deny-unlisten"
description = "Denies the unlisten command without any pre-configured scope."
commands.deny = ["unlisten"]
