# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-visible"
description = "Enables the set_visible command without any pre-configured scope."
commands.allow = ["set_visible"]

[[permission]]
identifier = "deny-set-visible"
description = "Denies the set_visible command without any pre-configured scope."
commands.deny = ["set_visible"]
