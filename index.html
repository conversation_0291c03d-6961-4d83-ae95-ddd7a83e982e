<!doctype html>
<html lang="">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON>er</title>
</head>

<body>
  <div id="app"></div>
  <script>
    ; (function () {
      const prefersDark =
        window.matchMedia &&
        window.matchMedia('(prefers-color-scheme: dark)').matches
      const setting = localStorage.getItem('arco-theme')
      let isDark = false

      if (setting) {
        isDark = setting === 'dark'
      } else {
        isDark = prefersDark
      }

      document.body.setAttribute('arco-theme', isDark ? 'dark' : 'light')
    })()
  </script>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>