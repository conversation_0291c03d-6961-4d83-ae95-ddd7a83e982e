{"rustc": 3062648155896360161, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 3303257178207977090, "deps": [[1615478164327904835, "pin_utils", false, 12176975304790260228], [1906322745568073236, "pin_project_lite", false, 13255911346125517441], [6955678925937229351, "slab", false, 9325497284328677043], [7620660491849607393, "futures_core", false, 6369687909189083715], [10565019901765856648, "futures_macro", false, 18438677738814295495], [16240732885093539806, "futures_task", false, 10465639023112420127]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-8b28967eae9556b4\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}