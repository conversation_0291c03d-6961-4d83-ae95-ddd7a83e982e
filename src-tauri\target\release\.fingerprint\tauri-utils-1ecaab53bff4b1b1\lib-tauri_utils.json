{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 12518800248535767337, "deps": [[561782849581144631, "html5ever", false, 18048911029064030654], [1200537532907108615, "url<PERSON><PERSON>n", false, 3427761113895583051], [3129130049864710036, "memchr", false, 8568752842674330861], [3150220818285335163, "url", false, 12933449415935540558], [3191507132440681679, "serde_untagged", false, 280401694172096958], [4899080583175475170, "semver", false, 3320579452684316696], [5986029879202738730, "log", false, 6945410041022675698], [6213549728662707793, "serde_with", false, 8449187331909077273], [6262254372177975231, "kuchiki", false, 14862931623177961011], [6606131838865521726, "ctor", false, 6013130802142784363], [7170110829644101142, "json_patch", false, 5992641861551906505], [8319709847752024821, "uuid", false, 3892896083864448984], [8786711029710048183, "toml", false, 17291003832683892246], [9010263965687315507, "http", false, 6524139142051382750], [9451456094439810778, "regex", false, 745639814905540488], [9689903380558560274, "serde", false, 8354309781181319556], [10806645703491011684, "thiserror", false, 17041937681748947287], [11989259058781683633, "dunce", false, 9913024412657547153], [13625485746686963219, "anyhow", false, 8525325201207834930], [14132538657330703225, "brotli", false, 17868203624876583527], [15367738274754116744, "serde_json", false, 12638398083885037052], [15622660310229662834, "walkdir", false, 12307803783780449492], [17146114186171651583, "infer", false, 12715696061038277801], [17155886227862585100, "glob", false, 9068506254070855372], [17186037756130803222, "phf", false, 3217127271573018165]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-1ecaab53bff4b1b1\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}