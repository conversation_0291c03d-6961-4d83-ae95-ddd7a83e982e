{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 4872535670476380077, "deps": [[376837177317575824, "build_script_build", false, 1950416000795194497], [4143744114649553716, "raw_window_handle", false, 14026015622225031953], [5986029879202738730, "log", false, 6945410041022675698], [10281541584571964250, "windows_sys", false, 3544974146096123654]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-00c6c2a2c1c9ed64\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}