{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 1288308961366000512, "deps": [[442785307232013896, "build_script_build", false, 11191501444909775907], [3150220818285335163, "url", false, 12933449415935540558], [4143744114649553716, "raw_window_handle", false, 14026015622225031953], [7606335748176206944, "dpi", false, 474726339341982742], [9010263965687315507, "http", false, 6524139142051382750], [9689903380558560274, "serde", false, 8354309781181319556], [10806645703491011684, "thiserror", false, 17041937681748947287], [11050281405049894993, "tauri_utils", false, 3635780141620643065], [13116089016666501665, "windows", false, 17693621379593172598], [15367738274754116744, "serde_json", false, 12638398083885037052], [16727543399706004146, "cookie", false, 10390345828029624578]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-00ccef051d3b2566\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}