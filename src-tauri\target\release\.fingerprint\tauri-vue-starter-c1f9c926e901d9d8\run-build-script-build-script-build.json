{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 8478282760992486277], [3935545708480822364, "build_script_build", false, 10022611123679359557], [9968676344646433769, "build_script_build", false, 9330644007050133304]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-vue-starter-c1f9c926e901d9d8\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}