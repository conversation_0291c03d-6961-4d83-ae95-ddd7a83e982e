{"rustc": 3062648155896360161, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 12844171384082609526, "deps": [[4899080583175475170, "semver", false, 9889865680198083971], [6913375703034175521, "schemars", false, 2326921781657810665], [7170110829644101142, "json_patch", false, 7396632103335079930], [8786711029710048183, "toml", false, 16660417584647497798], [9689903380558560274, "serde", false, 7001324394923115962], [11050281405049894993, "tauri_utils", false, 7509034364537852423], [12714016054753183456, "tauri_winres", false, 4035390353535025523], [13077543566650298139, "heck", false, 7238350878503789985], [13475171727366188400, "cargo_toml", false, 16336725645361898857], [13625485746686963219, "anyhow", false, 17322817805188435657], [15367738274754116744, "serde_json", false, 6545467989993011207], [15622660310229662834, "walkdir", false, 2170879548194002287], [16928111194414003569, "dirs", false, 6969773439021329379], [17155886227862585100, "glob", false, 5740912556682096171]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-b5f155f982879c50\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}