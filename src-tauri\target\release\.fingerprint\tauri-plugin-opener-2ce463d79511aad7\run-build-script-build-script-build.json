{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 8478282760992486277], [3935545708480822364, "build_script_build", false, 7183786909891432583]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-opener-2ce463d79511aad7\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}