["\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\available_monitors.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\center.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\close.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\create.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\current_monitor.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\cursor_position.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\destroy.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\get_all_windows.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\hide.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\inner_position.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\inner_size.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\internal_toggle_maximize.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_always_on_top.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_closable.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_decorated.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_focused.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_fullscreen.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_maximizable.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_maximized.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_minimizable.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_minimized.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_resizable.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\is_visible.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\maximize.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\minimize.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\monitor_from_point.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\outer_position.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\outer_size.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\primary_monitor.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\request_user_attention.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\scale_factor.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_bottom.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_top.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_background_color.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_badge_count.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_badge_label.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_closable.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_content_protected.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_grab.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_icon.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_position.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_visible.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_decorations.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_effects.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_focus.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_fullscreen.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_ignore_cursor_events.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_max_size.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_maximizable.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_min_size.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_minimizable.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_overlay_icon.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_position.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_progress_bar.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_resizable.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_shadow.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_size.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_size_constraints.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_skip_taskbar.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_theme.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_title.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_title_bar_style.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\set_visible_on_all_workspaces.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\show.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\start_dragging.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\start_resize_dragging.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\theme.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\title.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\toggle_maximize.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\unmaximize.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\commands\\unminimize.toml", "\\\\?\\D:\\qs-stock\\src-tauri\\target\\release\\build\\tauri-703647f38318bf32\\out\\permissions\\window\\autogenerated\\default.toml"]