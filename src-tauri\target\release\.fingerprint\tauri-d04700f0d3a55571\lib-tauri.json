{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 2484673656501667652, "deps": [[40386456601120721, "percent_encoding", false, 3021201692188075035], [442785307232013896, "tauri_runtime", false, 8332037655137749387], [1200537532907108615, "url<PERSON><PERSON>n", false, 3427761113895583051], [3150220818285335163, "url", false, 12933449415935540558], [4143744114649553716, "raw_window_handle", false, 14026015622225031953], [4341921533227644514, "muda", false, 1029021370330001221], [4919829919303820331, "serialize_to_javascript", false, 13889452699383445648], [5986029879202738730, "log", false, 6945410041022675698], [7752760652095876438, "tauri_runtime_wry", false, 2803835445652013672], [8539587424388551196, "webview2_com", false, 4012651261636751816], [9010263965687315507, "http", false, 6524139142051382750], [9228235415475680086, "tauri_macros", false, 5481633852266074840], [9538054652646069845, "tokio", false, 447478222455335956], [9689903380558560274, "serde", false, 8354309781181319556], [9920160576179037441, "getrandom", false, 11055968252565686114], [10229185211513642314, "mime", false, 11089388445269772631], [10629569228670356391, "futures_util", false, 5875650049630802809], [10755362358622467486, "build_script_build", false, 8478282760992486277], [10806645703491011684, "thiserror", false, 17041937681748947287], [11050281405049894993, "tauri_utils", false, 3635780141620643065], [11989259058781683633, "dunce", false, 9913024412657547153], [12565293087094287914, "window_vibrancy", false, 7750931893694571187], [12986574360607194341, "serde_repr", false, 16247552134434516191], [13077543566650298139, "heck", false, 7781695670833094828], [13116089016666501665, "windows", false, 17693621379593172598], [13625485746686963219, "anyhow", false, 8525325201207834930], [15367738274754116744, "serde_json", false, 12638398083885037052], [16928111194414003569, "dirs", false, 7783074338996995852], [17155886227862585100, "glob", false, 9068506254070855372]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-d04700f0d3a55571\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}