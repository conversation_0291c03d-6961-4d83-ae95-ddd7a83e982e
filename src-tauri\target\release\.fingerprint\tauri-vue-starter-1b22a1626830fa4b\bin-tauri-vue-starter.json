{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 10532448453723869356, "profile": 2040997289075261528, "path": 4942398508502643691, "deps": [[3935545708480822364, "tauri_plugin_opener", false, 5079918511705491996], [9689903380558560274, "serde", false, 8354309781181319556], [9968676344646433769, "tauri_starter_lib", false, 18263155257245766159], [9968676344646433769, "build_script_build", false, 16172331508728939108], [10755362358622467486, "tauri", false, 14919982421969691205], [15367738274754116744, "serde_json", false, 12638398083885037052]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-vue-starter-1b22a1626830fa4b\\dep-bin-tauri-vue-starter", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}